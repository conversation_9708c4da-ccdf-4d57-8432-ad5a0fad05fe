<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<VBox alignment="CENTER" spacing="20.0" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.javafxapp.HelloController">
   <children>
      <!-- Logo区域 -->
      <VBox alignment="CENTER" spacing="10.0">
         <children>
            <ImageView fx:id="logoImageView" fitHeight="200.0" fitWidth="200.0" pickOnBounds="true" preserveRatio="true" />
         </children>
      </VBox>
      
      <!-- 标题区域 -->
      <VBox alignment="CENTER" spacing="15.0">
         <children>
            <Label styleClass="title-label" text="JavaFX 桌面应用程序">
               <font>
                  <Font name="System Bold" size="28.0" />
               </font>
            </Label>
            
            <Label fx:id="welcomeLabel" styleClass="welcome-label" text="欢迎使用 JavaFX 桌面应用程序！" textAlignment="CENTER" wrapText="true">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
         </children>
      </VBox>
      
      <!-- 按钮区域 -->
      <HBox alignment="CENTER" spacing="20.0">
         <children>
            <Button fx:id="clickButton" onAction="#onButtonClick" styleClass="primary-button" text="点击我！">
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
            
            <Button onAction="#onResetClick" styleClass="secondary-button" text="重置">
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
         </children>
      </HBox>
      
      <!-- 底部信息 -->
      <VBox alignment="CENTER" spacing="5.0">
         <children>
            <Label styleClass="info-label" text="窗口大小: 1280 x 720" textFill="#666666">
               <font>
                  <Font size="12.0" />
               </font>
            </Label>
            <Label styleClass="info-label" text="支持自定义PNG格式图标" textFill="#666666">
               <font>
                  <Font size="12.0" />
               </font>
            </Label>
         </children>
      </VBox>
   </children>
   <padding>
      <Insets bottom="40.0" left="40.0" right="40.0" top="40.0" />
   </padding>
</VBox>
